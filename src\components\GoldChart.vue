<script lang="ts" setup>
import dayjs from 'dayjs'
import { computed, onMounted, ref, watch } from 'vue'
import { getMobileChartWrapperStyle, getMobileContainerStyle } from '@/utils/mobileChartConfig'

interface ChartDataPoint {
  time: string
  open: number
  high: number
  low: number
  close: number
  volume?: number
  // 分时图特有字段
  buy?: number
  sell?: number
  diffper?: number
}

interface Props {
  chartData: ChartDataPoint[]
  timeFrame: string
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

const selectedPoint = ref<ChartDataPoint | null>(null)
const canvasId = ref(`goldChart_${Date.now()}`)

// 图表类型
const chartType = computed(() => {
  return props.timeFrame === 'time' ? 'line' : 'candle'
})

// uCharts 配置选项 - 使用移动端优化配置
const chartOptions = computed(() => {
  const mobileConfig = {
    rotate: false,
    rotateLock: true,
    color: ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'],
    padding: [15, 15, 15, 20],
    dataLabel: false,
    enableScroll: true,
    enableMarkLine: true,
    legend: {},
    scrollPosition: 'right',
    xAxis: {
      labelCount: 4,
      itemCount: 40,
      disableGrid: true,
      gridColor: '#CCCCCC',
      gridType: 'solid',
      dashLength: 4,
      scrollShow: true,
      scrollAlign: 'right',
      scrollColor: '#A6A6A6',
      scrollBackgroundColor: '#EFEBEF',
    },
    yAxis: {
      disabled: false,
      gridType: 'dash',
      showTitle: false,
      axisLine: false,
    },
    extra: {
      candle: {
        color: {
          upLine: '#f04864',
          upFill: '#f04864',
          downLine: '#2fc25b',
          downFill: '#2fc25b',
        },
        average: {
          show: false,
          day: [5, 20],
          color: [
            '#1890ff',
            '#2fc25b',
            '#facc14',
          ],
        },
      },
      markLine: {
        type: 'dash',
        dashLength: 5,
        data: [
          {
            value: 2150,
            lineColor: '#f04864',
            showLabel: true,
          },
          {
            value: 2350,
            lineColor: '#f04864',
            showLabel: true,
          },
        ],
      },
      tooltip: {
        showCategory: true,
      },
    },
  }

  // 生成 X 轴数据
  const categories = props.chartData && props.chartData.length > 0
    ? props.chartData.map(item => formatTime(item.time))
    : []

  return {
    // 金色主题色彩搭配
    color: ['#D4AF37', '#B8860B', '#DAA520', '#CD853F', '#F4A460', '#DEB887', '#D2691E', '#A0522D', '#8B4513'],
    ...mobileConfig,
    // 确保 xAxis 有 data 属性
    xAxis: {
      ...mobileConfig.xAxis,
      data: categories,
    },
  }
})

// 容器样式
const containerStyle = computed(() => getMobileContainerStyle())

// 图表包装器样式
const chartWrapperStyle = computed(() => getMobileChartWrapperStyle())

// 转换数据为 uCharts 格式
const uChartsData = computed(() => {
  if (!props.chartData || props.chartData.length === 0) {
    return {
      categories: [],
      series: [],
    }
  }

  try {
    const categories = props.chartData.map((item) => {
      if (!item || !item.time) {
        console.warn('Invalid chart data item:', item)
        return ''
      }
      return formatTime(item.time)
    })

    if (props.timeFrame === 'time') {
      // 分时图 - 使用线图
      const lineData = props.chartData.map((item) => {
        if (!item || typeof item.close !== 'number') {
          console.warn('Invalid close price data:', item)
          return 0
        }
        return item.close
      })

      return {
        categories,
        series: [{
          name: '价格',
          data: lineData,
          color: '#ffc107',
        }],
      }
    }
    else {
      // K线图 - 使用蜡烛图 (正确的数据顺序: [open, high, low, close])
      const candleData = props.chartData.map((item) => {
        if (!item || typeof item.open !== 'number' || typeof item.high !== 'number'
          || typeof item.low !== 'number' || typeof item.close !== 'number') {
          console.warn('Invalid candle data:', item)
          return [0, 0, 0, 0]
        }
        // uCharts K线数据格式: [开盘价, 最高价, 最低价, 收盘价]
        return [item.open, item.high, item.low, item.close]
      })

      return {
        categories,
        series: [{
          name: '上证指数',
          data: candleData,
        }],
      }
    }
  }
  catch (error) {
    console.error('Error processing chart data:', error)
    return {
      categories: [],
      series: [],
    }
  }
})

// 计算最新价格
function getLatestPrice() {
  if (!props.chartData || props.chartData.length === 0)
    return '--'
  const latest = props.chartData[props.chartData.length - 1]
  return latest.close.toFixed(2)
}

// 计算价格变化
function getChangeValue() {
  if (!props.chartData || props.chartData.length < 2)
    return '--'
  const latest = props.chartData[props.chartData.length - 1]
  const previous = props.chartData[props.chartData.length - 2]
  const change = latest.close - previous.close
  const sign = change > 0 ? '+' : ''
  return `${sign}${change.toFixed(2)}`
}

// 计算涨跌幅
function getChangePercent() {
  if (!props.chartData || props.chartData.length < 2)
    return '--'
  const latest = props.chartData[props.chartData.length - 1]
  const previous = props.chartData[props.chartData.length - 2]
  const change = latest.close - previous.close
  const percent = (change / previous.close) * 100
  const sign = percent > 0 ? '+' : ''
  return `${sign}${percent.toFixed(2)}%`
}

// 获取最新价格样式类
function getLatestPriceClass() {
  if (!props.chartData || props.chartData.length < 2)
    return 'neutral'
  const latest = props.chartData[props.chartData.length - 1]
  const previous = props.chartData[props.chartData.length - 2]
  const change = latest.close - previous.close
  if (change > 0)
    return 'up'
  if (change < 0)
    return 'down'
  return 'neutral'
}

// 获取变化样式类
function getChangeClass() {
  return getLatestPriceClass()
}

// 获取价格变化样式类
function getPriceChangeClass(point: ChartDataPoint) {
  const change = point.close - point.open
  if (change > 0)
    return 'up'
  if (change < 0)
    return 'down'
  return 'neutral'
}

// 格式化时间
function formatTime(timeStr: string) {
  try {
    const date = new Date(timeStr)
    if (props.timeFrame === 'time') {
      // 分时图显示时:分
      return dayjs(date).format('YY-MM-DD HH:mm')
    }
    else {
      // K线图显示月-日
      return dayjs(date).format('YY-MM-DD')
    }
  }
  catch (error) {
    return timeStr
  }
}

// 格式化成交量
function formatVolume(volume: number) {
  if (volume >= 10000) {
    return `${(volume / 10000).toFixed(1)}万`
  }
  return volume.toString()
}

// 简化的数据点选择
function selectDataPoint(index?: number) {
  try {
    if (!props.chartData || props.chartData.length === 0) {
      return
    }

    const targetIndex = index !== undefined ? index : props.chartData.length - 1

    if (targetIndex >= 0 && targetIndex < props.chartData.length) {
      const dataPoint = props.chartData[targetIndex]
      if (dataPoint && typeof dataPoint === 'object') {
        selectedPoint.value = dataPoint
      }
    }
  }
  catch (error) {
    console.error('Error selecting data point:', error)
  }
}

// 手机端触摸事件处理
const touchStartTime = ref(0)
const touchStartX = ref(0)
const touchStartY = ref(0)

function onTouchStart(event: TouchEvent) {
  touchStartTime.value = Date.now()
  if (event.touches && event.touches.length > 0) {
    touchStartX.value = event.touches[0].clientX
    touchStartY.value = event.touches[0].clientY
  }
}

function onTouchMove(event: TouchEvent) {
  // 防止页面滚动干扰图表交互
  if (event.touches && event.touches.length > 0) {
    const deltaX = Math.abs(event.touches[0].clientX - touchStartX.value)
    const deltaY = Math.abs(event.touches[0].clientY - touchStartY.value)

    // 如果是水平滑动，阻止默认行为
    if (deltaX > deltaY) {
      event.preventDefault()
    }
  }
}

function onTouchEnd() {
  const touchEndTime = Date.now()
  const touchDuration = touchEndTime - touchStartTime.value

  // 短时间触摸视为点击
  if (touchDuration < 300) {
    selectDataPoint() // 选择最新的数据点
  }
}

// 监听数据变化
watch(() => props.chartData, () => {
  if (props.chartData && props.chartData.length > 0) {
    selectedPoint.value = props.chartData[props.chartData.length - 1]
  }
}, { immediate: true })

// 监听时间框架变化
watch(() => props.timeFrame, () => {
  // 时间框架变化时重新设置选中点
  if (props.chartData && props.chartData.length > 0) {
    selectedPoint.value = props.chartData[props.chartData.length - 1]
  }
})

onMounted(() => {
  // 初始化选中点
  if (props.chartData && props.chartData.length > 0) {
    selectedPoint.value = props.chartData[props.chartData.length - 1]
  }
})
</script>

<template>
  <view class="chart-container" :style="containerStyle">
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner" />
      <text class="loading-text">
        加载中...
      </text>
    </view>

    <view v-else-if="!chartData || chartData.length === 0" class="empty-container">
      <text class="empty-text">
        暂无数据
      </text>
    </view>

    <view v-else class="chart-content">
      <!-- 价格信息显示 -->
      <view v-if="selectedPoint" class="price-info">
        <view class="price-info-item">
          <text class="price-label">
            时间:
          </text>
          <text class="price-value">
            {{ formatTime(selectedPoint.time) }}
          </text>
        </view>
        <view class="price-info-item">
          <text class="price-label">
            开盘:
          </text>
          <text class="price-value">
            {{ selectedPoint.open.toFixed(2) }}
          </text>
        </view>
        <view class="price-info-item">
          <text class="price-label">
            收盘:
          </text>
          <text class="price-value" :class="getPriceChangeClass(selectedPoint)">
            {{ selectedPoint.close.toFixed(2) }}
          </text>
        </view>
        <view class="price-info-item">
          <text class="price-label">
            最高:
          </text>
          <text class="price-value high">
            {{ selectedPoint.high.toFixed(2) }}
          </text>
        </view>
        <view class="price-info-item">
          <text class="price-label">
            最低:
          </text>
          <text class="price-value low">
            {{ selectedPoint.low.toFixed(2) }}
          </text>
        </view>
      </view>

      <!-- uCharts 图表组件 - 手机端优化 -->
      <view class="chart-wrapper" :style="chartWrapperStyle">
        <qiun-data-charts
          :type="chartType" :opts="chartOptions" :chart-data="uChartsData" :canvas2d="true"
          :canvas-id="canvasId" :disable-scroll="true" :ontouch="true" :onzoom="true" background="transparent"
        />
      </view>

      <!-- 图表统计信息 -->
      <view class="chart-stats">
        <view class="stat-item">
          <text class="stat-label">
            最新价
          </text>
          <text class="stat-value" :class="getLatestPriceClass()">
            {{ getLatestPrice() }}
          </text>
        </view>
        <view class="stat-item">
          <text class="stat-label">
            涨跌
          </text>
          <text class="stat-value" :class="getChangeClass()">
            {{ getChangeValue() }}
          </text>
        </view>
        <view class="stat-item">
          <text class="stat-label">
            涨跌幅
          </text>
          <text class="stat-value" :class="getChangeClass()">
            {{ getChangePercent() }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.chart-container {
  width: 100%;
  height: 800rpx;
  position: relative;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: 18rpx;
  overflow: hidden;
  border: 2rpx solid rgba(244, 162, 97, 0.2);
  backdrop-filter: blur(10px);
  box-shadow:
    0 4rpx 16rpx rgba(231, 111, 81, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.7);
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(244, 162, 97, 0.3);
  border-top: 4rpx solid #f4a261;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20rpx;
  color: #8d5524;
  font-size: 24rpx;
  font-weight: 500;
}

.chart-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.empty-text {
  color: #8d5524;
  font-size: 26rpx;
  font-weight: 500;
}

.price-info {
  padding: 24rpx;
  background: linear-gradient(135deg, rgba(244, 162, 97, 0.08) 0%, rgba(233, 196, 106, 0.05) 100%);
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  border-bottom: 2rpx solid rgba(244, 162, 97, 0.2);
}

.price-info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 100rpx;
}

.price-label {
  font-size: 20rpx;
  color: #8d5524;
  font-weight: 500;
}

.price-value {
  font-size: 22rpx;
  color: #a0522d;
  font-weight: 600;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  height: 400rpx;
  position: relative;
  /* 手机端触摸优化 */
  touch-action: pan-x;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  /* 防止长按选择 */
  -webkit-tap-highlight-color: transparent;
}

.chart-stats {
  padding: 24rpx;
  background: linear-gradient(135deg, rgba(244, 162, 97, 0.08) 0%, rgba(233, 196, 106, 0.05) 100%);
  display: flex;
  justify-content: space-around;
  border-top: 2rpx solid rgba(244, 162, 97, 0.2);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-label {
  font-size: 20rpx;
  color: #8d5524;
  display: block;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.stat-value {
  font-size: 24rpx;
  color: #a0522d;
  font-weight: 600;
}

/* 价格变化颜色 - 中国股市传统：红涨绿跌 */
.up {
  color: #dc143c !important;
  /* 深红色表示上涨 */
}

.down {
  color: #228b22 !important;
  /* 森林绿表示下跌 */
}

.neutral {
  color: #d4af37 !important;
  /* 金色表示持平 */
}

.high {
  color: #dc143c !important;
  /* 最高价用红色 */
}

.low {
  color: #228b22 !important;
  /* 最低价用绿色 */
}

/* 手机端响应式优化 */
@media screen and (max-width: 750px) {
  .chart-container {
    height: 700rpx;
    /* 手机端稍微降低高度 */
  }

  .price-info {
    padding: 20rpx;
    gap: 20rpx;
  }

  .price-info-item {
    min-width: 80rpx;
  }

  .price-label {
    font-size: 18rpx;
  }

  .price-value {
    font-size: 20rpx;
  }

  .chart-wrapper {
    height: 350rpx;
    /* 手机端图表高度 */
  }

  .chart-stats {
    padding: 20rpx;
  }

  .stat-label {
    font-size: 18rpx;
  }

  .stat-value {
    font-size: 22rpx;
  }
}

/* 小屏幕手机优化 */
@media screen and (max-width: 600px) {
  .chart-container {
    height: 650rpx;
  }

  .price-info {
    padding: 16rpx;
    gap: 16rpx;
  }

  .chart-wrapper {
    height: 320rpx;
  }

  .price-label,
  .stat-label {
    font-size: 16rpx;
  }

  .price-value,
  .stat-value {
    font-size: 18rpx;
  }
}
</style>
