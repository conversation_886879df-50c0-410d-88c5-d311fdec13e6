<script lang="ts" setup>
import { computed } from 'vue'

interface MarketData {
  type: string // 金价类型，如"伦敦金（现货黄金）"
  currentPrice: string // 当前价格
  previousSettlement: string // 前一交易日结算价
  buyPrice: string // 买入价
  sellPrice: string // 卖出价
  high: string // 最高价
  low: string // 最低价
  updateTime: string // 更新时间，如"20:08:00"
  unknown1: string // 未知字段1
  open: string // 开盘价
  unknown2: string // 未知字段2
  unknown3: string // 未知字段3
  unknown4: string // 未知字段4
  date: string // 日期，如"2025-06-25"
  name: string // 名称
  timestamp: number // 时间戳
  createTime: string // 创建时间，如"2025-06-25 20:08:00"
}

interface Props {
  marketData: MarketData
}

const props = defineProps<Props>()

// 计算价格变化趋势
const priceChangeClass = computed(() => {
  const change = Number.parseFloat(props.marketData.currentPrice) - Number.parseFloat(props.marketData.previousSettlement)
  if (change > 0)
    return 'up'
  if (change < 0)
    return 'down'
  return 'neutral'
})

// 格式化价格变化
function formatChange(currentPrice: string, previousSettlement: string): string {
  const change = Number.parseFloat(currentPrice) - Number.parseFloat(previousSettlement)
  const sign = change > 0 ? '+' : ''
  return `${sign}${change.toFixed(2)}`
}

// 格式化变化百分比
function formatChangePercent(currentPrice: string, previousSettlement: string): string {
  const change = Number.parseFloat(currentPrice) - Number.parseFloat(previousSettlement)
  const percent = (change / Number.parseFloat(previousSettlement)) * 100
  const sign = percent > 0 ? '+' : ''
  return `${sign}${percent.toFixed(2)}%`
}

// 获取价格变化图标
function getPriceChangeIcon(currentPrice: string, previousSettlement: string): string {
  const change = Number.parseFloat(currentPrice) - Number.parseFloat(previousSettlement)
  if (change > 0)
    return '↗'
  if (change < 0)
    return '↘'
  return '→'
}
</script>

<template>
  <view class="price-card" :class="priceChangeClass">
    <!-- 市场名称和变化指示器 -->
    <view class="card-header">
      <text class="market-name">
        {{ marketData.type }}
      </text>
      <view class="price-change" :class="priceChangeClass">
        <text class="change-icon">
          {{ getPriceChangeIcon(marketData.currentPrice, marketData.previousSettlement) }}
        </text>
      </view>
    </view>

    <!-- 核心价格信息 -->
    <view class="price-main">
      <text class="current-price">
        ¥{{ marketData.currentPrice }}
      </text>
      <view class="price-changes">
        <text class="change-value" :class="priceChangeClass">
          {{ formatChange(marketData.currentPrice, marketData.previousSettlement) }}
        </text>
        <text class="change-percent" :class="priceChangeClass">
          {{ formatChangePercent(marketData.currentPrice, marketData.previousSettlement) }}
        </text>
      </view>
    </view>

    <!-- 次要信息 - 紧凑显示 -->
    <view class="secondary-info">
      <view class="info-row">
        <view class="info-item">
          <text class="info-label">
            昨收
          </text>
          <text class="info-value">
            {{ marketData.previousSettlement }}
          </text>
        </view>
        <view class="info-item">
          <text class="info-label">
            今开
          </text>
          <text class="info-value">
            {{ marketData.open }}
          </text>
        </view>
      </view>

      <!-- 可折叠的详细信息 -->
      <view class="detail-info">
        <view class="info-row">
          <view class="info-item">
            <text class="info-label">
              最高
            </text>
            <text class="info-value high">
              {{ marketData.high }}
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">
              最低
            </text>
            <text class="info-value low">
              {{ marketData.low }}
            </text>
          </view>
        </view>
        <!--
        <view v-if="marketData.buyPrice && marketData.sellPrice" class="info-row">
          <view class="info-item">
            <text class="info-label">
              买入
            </text>
            <text class="info-value">
              {{ marketData.buyPrice }}
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">
              卖出
            </text>
            <text class="info-value">
              {{ marketData.sellPrice }}
            </text>
          </view>
        </view> -->
      </view>
    </view>

    <!-- 更新时间 -->
    <view class="update-time">
      {{ marketData.updateTime }}
    </view>
  </view>
</template>

<style scoped>
/* 三列布局卡片样式 */
.price-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.92) 100%);
  border-radius: 16rpx;
  padding: 12rpx;
  margin-bottom: 0;
  backdrop-filter: blur(20px);
  border: 2rpx solid rgba(244, 162, 97, 0.25);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 200rpx;
  box-shadow:
    0 4rpx 16rpx rgba(231, 111, 81, 0.08),
    0 1rpx 4rpx rgba(244, 162, 97, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
}

.price-card:hover {
  transform: translateY(-3rpx);
  box-shadow:
    0 8rpx 24rpx rgba(231, 111, 81, 0.12),
    0 2rpx 8rpx rgba(244, 162, 97, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
  border-color: rgba(244, 162, 97, 0.4);
}

.price-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #e76f51 0%, #f4a261 50%, #e9c46a 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.price-card:hover::before {
  opacity: 1;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.market-name {
  font-size: 22rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #8d5524 0%, #a0522d 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: #8d5524;
  line-height: 1.2;
  flex: 1;
}

.price-change {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(244, 162, 97, 0.2) 0%, rgba(233, 196, 106, 0.15) 100%);
  border: 1rpx solid rgba(244, 162, 97, 0.3);
  flex-shrink: 0;
}

.change-icon {
  font-size: 20rpx;
  font-weight: bold;
  color: #8d5524;
}

/* 核心价格信息 */
.price-main {
  margin-bottom: 12rpx;
  flex: 1;
}

.current-price {
  font-size: 32rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #8d5524 0%, #a0522d 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: #8d5524;
  display: block;
  margin-bottom: 6rpx;
  line-height: 1.2;
}

.price-changes {
  display: flex;
  gap: 8rpx;
  align-items: center;
  flex-wrap: wrap;
}

.change-value,
.change-percent {
  font-size: 20rpx;
  font-weight: 600;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, rgba(244, 162, 97, 0.1) 0%, rgba(233, 196, 106, 0.08) 100%);
  border: 1rpx solid rgba(244, 162, 97, 0.2);
  line-height: 1;
}

/* 次要信息区域 */
.secondary-info {
  margin-bottom: 8rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.info-label {
  font-size: 20rpx;
  color: #8d5524;
  margin-bottom: 2rpx;
  font-weight: 500;
  opacity: 0.8;
}

.info-value {
  font-size: 22rpx;
  color: #a0522d;
  font-weight: 600;
  line-height: 1.2;
}

/* 详细信息 - 可折叠 */
.detail-info {
  margin-top: 6rpx;
  padding-top: 6rpx;
  border-top: 1rpx solid rgba(244, 162, 97, 0.2);
}

/* 更新时间 */
.update-time {
  font-size: 18rpx;
  color: #8d5524;
  text-align: center;
  background: linear-gradient(135deg, rgba(244, 162, 97, 0.1) 0%, rgba(233, 196, 106, 0.08) 100%);
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  border: 1rpx solid rgba(244, 162, 97, 0.2);
  margin-top: auto;
  opacity: 0.9;
}

/* 价格变化颜色 - 中国股市传统习惯：红涨绿跌 */
.up {
  color: #dc143c !important; /* 红色表示上涨 */
}

.down {
  color: #228b22 !important; /* 绿色表示下跌 */
}

.neutral {
  color: #daa520 !important; /* 金黄色表示持平 */
}

.price-change.up {
  background: linear-gradient(135deg, rgba(220, 20, 60, 0.2) 0%, rgba(255, 182, 193, 0.15) 100%);
  border-color: rgba(220, 20, 60, 0.3);
}

.price-change.down {
  background: linear-gradient(135deg, rgba(34, 139, 34, 0.2) 0%, rgba(144, 238, 144, 0.15) 100%);
  border-color: rgba(34, 139, 34, 0.3);
}

.price-change.neutral {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.2) 0%, rgba(233, 196, 106, 0.15) 100%);
  border-color: rgba(218, 165, 32, 0.3);
}

.high {
  color: #dc143c !important; /* 最高价用红色 */
}

.low {
  color: #228b22 !important; /* 最低价用绿色 */
}

/* 响应式设计 */
@media (max-width: 750px) {
  .current-price {
    font-size: 28rpx;
  }

  .market-name {
    font-size: 20rpx;
  }

  .change-value,
  .change-percent {
    font-size: 18rpx;
    padding: 3rpx 6rpx;
  }

  .info-label {
    font-size: 18rpx;
  }

  .info-value {
    font-size: 20rpx;
  }
}
</style>
