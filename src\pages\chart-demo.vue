<script lang="ts" setup>
const opts = {
  rotate: false,
  rotateLock: false,
  color: ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'],
  padding: [15, 15, 0, 15],
  dataLabel: false,
  enableScroll: true,
  enableMarkLine: true,
  legend: {},
  xAxis: {
    labelCount: 4,
    itemCount: 40,
    disableGrid: true,
    gridColor: '#CCCCCC',
    gridType: 'solid',
    dashLength: 4,
    scrollShow: true,
    scrollAlign: 'left',
    scrollColor: '#A6A6A6',
    scrollBackgroundColor: '#EFEBEF',
  },
  yAxis: {},
  extra: {
    candle: {
      color: {
        upLine: '#f04864',
        upFill: '#f04864',
        downLine: '#2fc25b',
        downFill: '#2fc25b',
      },
      average: {
        show: true,
        name: [
          'MA5',
          'MA10',
          'MA30',
        ],
        day: [
          5,
          10,
          20,
        ],
        color: [
          '#1890ff',
          '#2fc25b',
          '#facc14',
        ],
      },
    },
    markLine: {
      type: 'dash',
      dashLength: 5,
      data: [
        {
          value: 2150,
          lineColor: '#f04864',
          showLabel: true,
        },
        {
          value: 2350,
          lineColor: '#f04864',
          showLabel: true,
        },
      ],
    },
    tooltip: {
      showCategory: true,
    },
  },
}

const chartData = {
  categories: ['2022/1/24', '2022/1/25', '2022/1/28', '2022/1/29', '2022/1/30', '2022/1/31', '2022/2/1', '2022/2/4', '2022/2/5', '2022/2/6', '2022/2/7', '2022/2/8', '2022/2/18', '2022/2/19', '2022/2/20', '2022/2/21', '2022/2/22', '2022/2/25', '2022/2/26', '2022/2/27', '2022/2/28', '2022/3/1', '2022/3/4', '2022/3/5', '2022/3/6', '2022/3/7', '2022/3/8', '2022/3/11', '2022/3/12', '2022/3/13', '2022/3/14', '2022/3/15', '2022/3/18', '2022/3/19', '2022/3/20', '2022/3/21', '2022/3/22', '2022/3/25', '2022/3/26', '2022/3/27', '2022/3/28', '2022/3/29', '2022/4/1', '2022/4/2', '2022/4/3', '2022/4/8', '2022/4/9', '2022/4/10', '2022/4/11', '2022/4/12', '2022/4/15', '2022/4/16', '2022/4/17', '2022/4/18', '2022/4/19', '2022/4/22', '2022/4/23', '2022/4/24', '2022/4/25', '2022/4/26', '2022/5/2', '2022/5/3', '2022/5/6', '2022/5/7', '2022/5/8', '2022/5/9', '2022/5/10', '2022/5/13', '2022/5/14', '2022/5/15', '2022/5/16', '2022/5/17', '2022/5/20', '2022/5/21', '2022/5/22', '2022/5/23', '2022/5/24', '2022/5/27', '2022/5/28', '2022/5/29', '2022/5/30', '2022/5/31', '2022/6/3', '2022/6/4', '2022/6/5', '2022/6/6', '2022/6/7', '2022/6/13'],
  series: [
    {
      name: '上证指数',
      data: [[2320.26, 2302.6, 2287.3, 2362.94], [2300, 2291.3, 2288.26, 2308.38], [2295.35, 2346.5, 2295.35, 2346.92], [2347.22, 2358.98, 2337.35, 2363.8], [2360.75, 2382.48, 2347.89, 2383.76], [2383.43, 2385.42, 2371.23, 2391.82], [2377.41, 2419.02, 2369.57, 2421.15], [2425.92, 2428.15, 2417.58, 2440.38], [2411, 2433.13, 2403.3, 2437.42], [2432.68, 2434.48, 2427.7, 2441.73], [2430.69, 2418.53, 2394.22, 2433.89], [2416.62, 2432.4, 2414.4, 2443.03], [2441.91, 2421.56, 2415.43, 2444.8], [2420.26, 2382.91, 2373.53, 2427.07], [2383.49, 2397.18, 2370.61, 2397.94], [2378.82, 2325.95, 2309.17, 2378.82], [2322.94, 2314.16, 2308.76, 2330.88], [2320.62, 2325.82, 2315.01, 2338.78], [2313.74, 2293.34, 2289.89, 2340.71], [2297.77, 2313.22, 2292.03, 2324.63], [2322.32, 2365.59, 2308.92, 2366.16], [2364.54, 2359.51, 2330.86, 2369.65], [2332.08, 2273.4, 2259.25, 2333.54], [2274.81, 2326.31, 2270.1, 2328.14], [2333.61, 2347.18, 2321.6, 2351.44], [2340.44, 2324.29, 2304.27, 2352.02], [2326.42, 2318.61, 2314.59, 2333.67], [2314.68, 2310.59, 2296.58, 2320.96], [2309.16, 2286.6, 2264.83, 2333.29], [2282.17, 2263.97, 2253.25, 2286.33], [2255.77, 2270.28, 2253.31, 2276.22], [2269.31, 2278.4, 2250, 2312.08], [2267.29, 2240.02, 2239.21, 2276.05], [2244.26, 2257.43, 2232.02, 2261.31], [2257.74, 2317.37, 2257.42, 2317.86], [2318.21, 2324.24, 2311.6, 2330.81], [2321.4, 2328.28, 2314.97, 2332], [2334.74, 2326.72, 2319.91, 2344.89], [2318.58, 2297.67, 2281.12, 2319.99], [2299.38, 2301.26, 2289, 2323.48], [2273.55, 2236.3, 2232.91, 2273.55], [2238.49, 2236.62, 2228.81, 2246.87], [2229.46, 2234.4, 2227.31, 2243.95], [2234.9, 2227.74, 2220.44, 2253.42], [2232.69, 2225.29, 2217.25, 2241.34], [2196.24, 2211.59, 2180.67, 2212.59], [2215.47, 2225.77, 2215.47, 2234.73], [2224.93, 2226.13, 2212.56, 2233.04], [2236.98, 2219.55, 2217.26, 2242.48], [2218.09, 2206.78, 2204.44, 2226.26], [2199.91, 2181.94, 2177.39, 2204.99], [2169.63, 2194.85, 2165.78, 2196.43], [2195.03, 2193.8, 2178.47, 2197.51], [2181.82, 2197.6, 2175.44, 2206.03], [2201.12, 2244.64, 2200.58, 2250.11], [2236.4, 2242.17, 2232.26, 2245.12], [2242.62, 2184.54, 2182.81, 2242.62], [2187.35, 2218.32, 2184.11, 2226.12], [2213.19, 2199.31, 2191.85, 2224.63], [2203.89, 2177.91, 2173.86, 2210.58], [2170.78, 2174.12, 2161.14, 2179.65], [2179.05, 2205.5, 2179.05, 2222.81], [2212.5, 2231.17, 2212.5, 2236.07], [2227.86, 2235.57, 2219.44, 2240.26], [2242.39, 2246.3, 2235.42, 2255.21], [2246.96, 2232.97, 2221.38, 2247.86], [2228.82, 2246.83, 2225.81, 2247.67], [2247.68, 2241.92, 2231.36, 2250.85], [2238.9, 2217.01, 2205.87, 2239.93], [2217.09, 2224.8, 2213.58, 2225.19], [2221.34, 2251.81, 2210.77, 2252.87], [2249.81, 2282.87, 2248.41, 2288.09], [2286.33, 2299.99, 2281.9, 2309.39], [2297.11, 2305.11, 2290.12, 2305.3], [2303.75, 2302.4, 2292.43, 2314.18], [2293.81, 2275.67, 2274.1, 2304.95], [2281.45, 2288.53, 2270.25, 2292.59], [2286.66, 2293.08, 2283.94, 2301.7], [2293.4, 2321.32, 2281.47, 2322.1], [2323.54, 2324.02, 2321.17, 2334.33], [2316.25, 2317.75, 2310.49, 2325.72], [2320.74, 2300.59, 2299.37, 2325.53], [2300.21, 2299.25, 2294.11, 2313.43], [2297.1, 2272.42, 2264.76, 2297.1], [2270.71, 2270.93, 2260.87, 2276.86], [2264.43, 2242.11, 2240.07, 2266.69], [2242.26, 2210.9, 2205.07, 2250.63], [2190.1, 2148.35, 2126.22, 2190.1]],
    },
  ],
}
</script>

<template>
  <view class="chart-demo-page">
    <view class="header">
      <text class="title">
        美化K线图演示
      </text>
      <text class="subtitle">
        金色主题 + 中国股市颜色传统
      </text>
    </view>

    <view class="chart-section">
      <view class="section-title">
        <text>官方默认样式对比</text>
      </view>

      <!-- 美化后的K线图 -->
      <view class="chart-container">
        <view class="chart-header">
          <text class="chart-title">
            美化版K线图
          </text>
          <text class="chart-desc">
            金色主题 · 红涨绿跌 · 移动平均线
          </text>
        </view>
        <qiun-data-charts
          type="candle"
          :opts="opts"
          :chart-data="chartData"
          :disable-scroll="true"
          :ontouch="true"
          :onzoom="true"
        />
      </view>

      <!-- 配置说明 -->
      <view class="config-info">
        <view class="info-title">
          <text>美化配置特点</text>
        </view>
        <view class="info-list">
          <view class="info-item">
            <text class="info-label">
              🎨 颜色主题：
            </text>
            <text class="info-value">
              金色系配色方案，温暖优雅
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">
              📈 涨跌颜色：
            </text>
            <text class="info-value">
              红涨绿跌（中国股市传统）
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">
              📊 移动平均线：
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">
              🎯 网格线：
            </text>
            <text class="info-value">
              虚线样式，金色半透明
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">
              💡 提示框：
            </text>
            <text class="info-value">
              圆角白色背景，金色边框
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">
              🔄 滚动条：
            </text>
            <text class="info-value">
              金色滚动条，右对齐显示
            </text>
          </view>
        </view>
      </view>

      <!-- 颜色说明 -->
      <view class="color-palette">
        <view class="palette-title">
          <text>配色方案</text>
        </view>
        <view class="color-grid">
          <view class="color-item">
            <view class="color-box" style="background: #DC143C;" />
            <text class="color-name">
              上涨红
            </text>
            <text class="color-code">
              #DC143C
            </text>
          </view>
          <view class="color-item">
            <view class="color-box" style="background: #228B22;" />
            <text class="color-name">
              下跌绿
            </text>
            <text class="color-code">
              #228B22
            </text>
          </view>
          <view class="color-item">
            <view class="color-box" style="background: #D4AF37;" />
            <text class="color-name">
              主金色
            </text>
            <text class="color-code">
              #D4AF37
            </text>
          </view>
          <view class="color-item">
            <view class="color-box" style="background: #B8860B;" />
            <text class="color-name">
              暗金色
            </text>
            <text class="color-code">
              #B8860B
            </text>
          </view>
          <view class="color-item">
            <view class="color-box" style="background: #CD853F;" />
            <text class="color-name">
              秘鲁色
            </text>
            <text class="color-code">
              #CD853F
            </text>
          </view>
          <view class="color-item">
            <view class="color-box" style="background: #8B4513;" />
            <text class="color-name">
              文字色
            </text>
            <text class="color-code">
              #8B4513
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.chart-demo-page {
  padding: 20rpx;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(244, 162, 97, 0.05) 50%,
    rgba(233, 196, 106, 0.08) 100%
  );
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx 20rpx;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: 18rpx;
  border: 2rpx solid rgba(244, 162, 97, 0.2);
  backdrop-filter: blur(10px);

  .title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #8b4513;
    margin-bottom: 12rpx;
  }

  .subtitle {
    font-size: 24rpx;
    color: #cd853f;
    font-weight: 500;
  }
}

.chart-section {
  .section-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #8b4513;
    margin-bottom: 20rpx;
    text-align: center;
  }
}

.chart-container {
  margin-bottom: 40rpx;

  .chart-header {
    padding: 20rpx;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(244, 162, 97, 0.08) 100%);
    border-radius: 12rpx 12rpx 0 0;
    border: 2rpx solid rgba(244, 162, 97, 0.2);
    border-bottom: none;

    .chart-title {
      display: block;
      font-size: 26rpx;
      font-weight: bold;
      color: #8b4513;
      margin-bottom: 8rpx;
    }

    .chart-desc {
      font-size: 20rpx;
      color: #cd853f;
    }
  }
}

.config-info {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: 18rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  border: 2rpx solid rgba(244, 162, 97, 0.2);
  backdrop-filter: blur(10px);

  .info-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #8b4513;
    margin-bottom: 24rpx;
    text-align: center;
  }

  .info-list {
    .info-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16rpx;
      padding: 12rpx;
      background: rgba(244, 162, 97, 0.05);
      border-radius: 8rpx;

      .info-label {
        font-size: 22rpx;
        color: #8b4513;
        font-weight: 600;
        min-width: 140rpx;
      }

      .info-value {
        font-size: 22rpx;
        color: #cd853f;
        flex: 1;
      }
    }
  }
}

.color-palette {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: 18rpx;
  padding: 30rpx;
  border: 2rpx solid rgba(244, 162, 97, 0.2);
  backdrop-filter: blur(10px);

  .palette-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #8b4513;
    margin-bottom: 24rpx;
    text-align: center;
  }

  .color-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;

    .color-item {
      text-align: center;
      padding: 16rpx;
      background: rgba(244, 162, 97, 0.05);
      border-radius: 12rpx;

      .color-box {
        width: 60rpx;
        height: 60rpx;
        border-radius: 8rpx;
        margin: 0 auto 12rpx;
        border: 2rpx solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      }

      .color-name {
        display: block;
        font-size: 20rpx;
        color: #8b4513;
        font-weight: 600;
        margin-bottom: 4rpx;
      }

      .color-code {
        font-size: 18rpx;
        color: #cd853f;
        font-family: monospace;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 750px) {
  .color-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}
</style>
