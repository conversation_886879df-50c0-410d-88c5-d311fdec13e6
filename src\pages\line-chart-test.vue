<route lang="json5">
{
  style: {
    navigationBarTitleText: '分时折线图测试',
    navigationBarBackgroundColor: '#F4A261',
    navigationBarTextStyle: 'white'
  },
}
</route>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import GoldChart from '@/components/GoldChart.vue'

const chartData = ref([])
const loading = ref(false)

// 生成分时折线图测试数据
function generateLineChartData() {
  const data = []
  const basePrice = 780
  const baseTime = new Date()
  
  // 生成一天的分时数据（每30分钟一个数据点）
  for (let i = 0; i < 48; i++) {
    const time = new Date(baseTime.getTime() - (48 - i) * 30 * 60 * 1000)
    const timeStr = time.toISOString()
    
    // 模拟价格波动 - 更平滑的趋势
    const timeProgress = i / 48
    const trendFactor = Math.sin(timeProgress * Math.PI * 2) * 10 // 正弦波趋势
    const randomFactor = (Math.random() - 0.5) * 5 // 随机波动
    const currentPrice = basePrice + trendFactor + randomFactor
    
    const dataPoint = {
      time: timeStr,
      open: Number((currentPrice - 1 + Math.random() * 2).toFixed(2)),
      high: Number((currentPrice + Math.random() * 3).toFixed(2)),
      low: Number((currentPrice - Math.random() * 3).toFixed(2)),
      close: Number(currentPrice.toFixed(2)), // 折线图主要显示这个值
      volume: Math.floor(Math.random() * 50000),
      // 分时图特有字段
      buy: Number((currentPrice - 2).toFixed(2)),
      sell: Number((currentPrice + 2).toFixed(2)),
      diffper: Number(((Math.random() - 0.5) * 2).toFixed(2)),
    }
    
    data.push(dataPoint)
  }
  
  return data
}

// 加载测试数据
function loadTestData() {
  loading.value = true
  
  setTimeout(() => {
    chartData.value = generateLineChartData()
    loading.value = false
  }, 500)
}

// 生成不同趋势的数据
function generateTrendData(trend: 'up' | 'down' | 'flat' | 'volatile') {
  const data = []
  const basePrice = 780
  const baseTime = new Date()
  
  for (let i = 0; i < 30; i++) {
    const time = new Date(baseTime.getTime() - (30 - i) * 60 * 60 * 1000)
    const timeStr = time.toISOString()
    
    let currentPrice = basePrice
    const progress = i / 30
    
    switch (trend) {
      case 'up':
        currentPrice = basePrice + progress * 20 + (Math.random() - 0.5) * 3
        break
      case 'down':
        currentPrice = basePrice - progress * 20 + (Math.random() - 0.5) * 3
        break
      case 'flat':
        currentPrice = basePrice + (Math.random() - 0.5) * 2
        break
      case 'volatile':
        currentPrice = basePrice + Math.sin(progress * Math.PI * 4) * 15 + (Math.random() - 0.5) * 5
        break
    }
    
    const dataPoint = {
      time: timeStr,
      open: Number((currentPrice - 1 + Math.random() * 2).toFixed(2)),
      high: Number((currentPrice + Math.random() * 2).toFixed(2)),
      low: Number((currentPrice - Math.random() * 2).toFixed(2)),
      close: Number(currentPrice.toFixed(2)),
      volume: Math.floor(Math.random() * 50000),
      buy: Number((currentPrice - 1).toFixed(2)),
      sell: Number((currentPrice + 1).toFixed(2)),
      diffper: Number(((currentPrice - basePrice) / basePrice * 100).toFixed(2)),
    }
    
    data.push(dataPoint)
  }
  
  chartData.value = data
}

onMounted(() => {
  loadTestData()
})
</script>

<template>
  <view class="line-chart-test">
    <view class="header">
      <text class="title">分时折线图测试</text>
      <text class="subtitle">验证分时图显示为折线图的效果</text>
    </view>

    <!-- 趋势测试按钮 -->
    <view class="trend-controls">
      <text class="control-title">趋势测试:</text>
      <view class="trend-buttons">
        <button class="trend-btn up" @click="generateTrendData('up')">上涨趋势</button>
        <button class="trend-btn down" @click="generateTrendData('down')">下跌趋势</button>
        <button class="trend-btn flat" @click="generateTrendData('flat')">横盘整理</button>
        <button class="trend-btn volatile" @click="generateTrendData('volatile')">剧烈波动</button>
      </view>
    </view>

    <!-- 控制按钮 -->
    <view class="controls">
      <button class="control-btn" @click="loadTestData">重新生成数据</button>
    </view>

    <!-- 数据信息 -->
    <view class="data-info">
      <view class="info-row">
        <text class="info-label">图表类型:</text>
        <text class="info-value">分时折线图</text>
      </view>
      <view class="info-row">
        <text class="info-label">数据点数:</text>
        <text class="info-value">{{ chartData.length }}</text>
      </view>
      <view class="info-row">
        <text class="info-label">价格范围:</text>
        <text class="info-value" v-if="chartData.length > 0">
          {{ Math.min(...chartData.map(d => d.close)).toFixed(2) }} - 
          {{ Math.max(...chartData.map(d => d.close)).toFixed(2) }}
        </text>
      </view>
    </view>

    <!-- 图表组件 -->
    <view class="chart-section">
      <GoldChart 
        :chart-data="chartData" 
        time-frame="time" 
        :loading="loading" 
      />
    </view>

    <!-- 说明 -->
    <view class="instructions">
      <view class="instruction-title">测试说明:</view>
      <view class="instruction-item">
        <text class="instruction-text">1. 分时图现在显示为平滑的折线图</text>
      </view>
      <view class="instruction-item">
        <text class="instruction-text">2. 折线图显示价格的连续变化趋势</text>
      </view>
      <view class="instruction-item">
        <text class="instruction-text">3. 可以测试不同的价格趋势效果</text>
      </view>
      <view class="instruction-item">
        <text class="instruction-text">4. 支持触摸交互查看具体数据点</text>
      </view>
    </view>

    <!-- 当前数据示例 -->
    <view class="current-data" v-if="chartData.length > 0">
      <view class="data-title">最新数据点:</view>
      <view class="data-content">
        <text class="data-text">
时间: {{ new Date(chartData[chartData.length - 1].time).toLocaleTimeString() }}
价格: {{ chartData[chartData.length - 1].close }}
涨跌幅: {{ chartData[chartData.length - 1].diffper }}%
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.line-chart-test {
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(244, 162, 97, 0.05) 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;

  .title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #8B4513;
    margin-bottom: 10rpx;
  }

  .subtitle {
    font-size: 24rpx;
    color: #A0522D;
  }
}

.trend-controls {
  margin-bottom: 20rpx;

  .control-title {
    display: block;
    font-size: 26rpx;
    font-weight: bold;
    color: #8B4513;
    margin-bottom: 16rpx;
  }

  .trend-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;

    .trend-btn {
      padding: 12rpx 20rpx;
      border: none;
      border-radius: 20rpx;
      font-size: 22rpx;
      color: white;
      font-weight: 500;

      &.up {
        background: linear-gradient(135deg, #DC143C 0%, #FF6B6B 100%);
      }

      &.down {
        background: linear-gradient(135deg, #228B22 0%, #32CD32 100%);
      }

      &.flat {
        background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
      }

      &.volatile {
        background: linear-gradient(135deg, #FF8C00 0%, #FFD700 100%);
      }
    }
  }
}

.controls {
  display: flex;
  justify-content: center;
  margin-bottom: 30rpx;

  .control-btn {
    padding: 16rpx 32rpx;
    background: linear-gradient(135deg, #F4A261 0%, #E9C46A 100%);
    color: white;
    border: none;
    border-radius: 25rpx;
    font-size: 24rpx;
    font-weight: 500;
    box-shadow: 0 4rpx 12rpx rgba(244, 162, 97, 0.3);
  }
}

.data-info {
  background: rgba(244, 162, 97, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid rgba(244, 162, 97, 0.2);

  .info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12rpx;

    .info-label {
      font-size: 24rpx;
      color: #A0522D;
      font-weight: 500;
    }

    .info-value {
      font-size: 24rpx;
      color: #8B4513;
      font-weight: 600;
    }
  }
}

.chart-section {
  margin-bottom: 30rpx;
}

.instructions {
  background: rgba(233, 196, 106, 0.1);
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid rgba(233, 196, 106, 0.2);

  .instruction-title {
    font-size: 26rpx;
    font-weight: bold;
    color: #8B4513;
    margin-bottom: 16rpx;
  }

  .instruction-item {
    margin-bottom: 12rpx;

    .instruction-text {
      font-size: 22rpx;
      color: #A0522D;
      line-height: 1.5;
    }
  }
}

.current-data {
  background: rgba(220, 20, 60, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
  border: 2rpx solid rgba(220, 20, 60, 0.2);

  .data-title {
    font-size: 26rpx;
    font-weight: bold;
    color: #8B4513;
    margin-bottom: 12rpx;
  }

  .data-content {
    .data-text {
      font-family: 'Courier New', monospace;
      font-size: 20rpx;
      color: #2D5016;
      line-height: 1.6;
      white-space: pre-line;
    }
  }
}
</style>
