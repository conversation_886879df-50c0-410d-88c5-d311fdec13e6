<route lang="json5">
{
  style: {
    navigationBarTitleText: 'X轴数据测试',
    navigationBarBackgroundColor: '#F4A261',
    navigationBarTextStyle: 'white'
  },
}
</route>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import GoldChart from '@/components/GoldChart.vue'

const chartData = ref([])
const activeTab = ref('1day')
const debugInfo = ref({
  chartDataLength: 0,
  xAxisData: [],
  chartOptions: null,
})

// 生成测试数据
function generateTestData(timeFrame: string) {
  const data = []
  const basePrice = 2000
  const dataCount = timeFrame === 'time' ? 10 : 15
  
  for (let i = 0; i < dataCount; i++) {
    const time = timeFrame === 'time' 
      ? new Date(Date.now() - (dataCount - i) * 60000).toISOString() // 分钟数据
      : new Date(Date.now() - (dataCount - i) * 86400000).toISOString() // 日数据
    
    const open = basePrice + (Math.random() - 0.5) * 50
    const close = open + (Math.random() - 0.5) * 30
    const high = Math.max(open, close) + Math.random() * 20
    const low = Math.min(open, close) - Math.random() * 20
    
    data.push({
      time,
      open: Number(open.toFixed(2)),
      high: Number(high.toFixed(2)),
      low: Number(low.toFixed(2)),
      close: Number(close.toFixed(2)),
      volume: Math.floor(Math.random() * 100000),
    })
  }
  
  return data
}

// 格式化时间（与组件中的函数保持一致）
function formatTime(timeStr: string, timeFrame: string) {
  try {
    const date = new Date(timeStr)
    if (timeFrame === 'time') {
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    } else {
      return `${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
    }
  } catch (error) {
    return timeStr
  }
}

// 更新调试信息
function updateDebugInfo() {
  debugInfo.value = {
    chartDataLength: chartData.value.length,
    xAxisData: chartData.value.map(item => formatTime(item.time, activeTab.value)),
    chartOptions: null, // 这里可以添加更多配置信息
  }
}

// 切换时间框架
function switchTimeFrame(timeFrame: string) {
  activeTab.value = timeFrame
  chartData.value = generateTestData(timeFrame)
  updateDebugInfo()
}

// 测试不同数据量
function testDataCount(count: number) {
  const data = []
  const basePrice = 2000
  
  for (let i = 0; i < count; i++) {
    const time = new Date(Date.now() - (count - i) * 86400000).toISOString()
    const open = basePrice + (Math.random() - 0.5) * 50
    const close = open + (Math.random() - 0.5) * 30
    const high = Math.max(open, close) + Math.random() * 20
    const low = Math.min(open, close) - Math.random() * 20
    
    data.push({
      time,
      open: Number(open.toFixed(2)),
      high: Number(high.toFixed(2)),
      low: Number(low.toFixed(2)),
      close: Number(close.toFixed(2)),
      volume: Math.floor(Math.random() * 100000),
    })
  }
  
  chartData.value = data
  updateDebugInfo()
}

onMounted(() => {
  switchTimeFrame('1day')
})
</script>

<template>
  <view class="xaxis-test">
    <view class="header">
      <text class="title">X轴数据配置测试</text>
      <text class="subtitle">验证 chartOptions.xAxis.data 是否正确赋值</text>
    </view>

    <!-- 时间框架切换 -->
    <view class="time-tabs">
      <button 
        class="tab-btn" 
        :class="{ active: activeTab === 'time' }"
        @click="switchTimeFrame('time')"
      >
        分时图
      </button>
      <button 
        class="tab-btn" 
        :class="{ active: activeTab === '1day' }"
        @click="switchTimeFrame('1day')"
      >
        日K线
      </button>
    </view>

    <!-- 数据量测试 -->
    <view class="data-count-tests">
      <text class="section-title">数据量测试:</text>
      <view class="count-buttons">
        <button class="count-btn" @click="testDataCount(5)">5条</button>
        <button class="count-btn" @click="testDataCount(10)">10条</button>
        <button class="count-btn" @click="testDataCount(20)">20条</button>
        <button class="count-btn" @click="testDataCount(50)">50条</button>
      </view>
    </view>

    <!-- 调试信息 -->
    <view class="debug-info">
      <view class="debug-title">调试信息:</view>
      <view class="debug-item">
        <text class="debug-label">数据条数:</text>
        <text class="debug-value">{{ debugInfo.chartDataLength }}</text>
      </view>
      <view class="debug-item">
        <text class="debug-label">时间框架:</text>
        <text class="debug-value">{{ activeTab }}</text>
      </view>
      <view class="debug-item">
        <text class="debug-label">X轴数据:</text>
        <text class="debug-value small">{{ debugInfo.xAxisData.join(', ') }}</text>
      </view>
    </view>

    <!-- 图表组件 -->
    <view class="chart-section">
      <GoldChart 
        :chart-data="chartData" 
        :time-frame="activeTab" 
        :loading="false" 
      />
    </view>

    <!-- 说明 -->
    <view class="instructions">
      <view class="instruction-title">测试说明:</view>
      <view class="instruction-item">
        <text>1. 切换不同时间框架，观察X轴标签是否正确显示</text>
      </view>
      <view class="instruction-item">
        <text>2. 测试不同数据量，检查X轴数据是否完整</text>
      </view>
      <view class="instruction-item">
        <text>3. 查看调试信息中的X轴数据是否与图表一致</text>
      </view>
      <view class="instruction-item">
        <text>4. 检查控制台是否有相关日志输出</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.xaxis-test {
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(244, 162, 97, 0.05) 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;

  .title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #8B4513;
    margin-bottom: 10rpx;
  }

  .subtitle {
    font-size: 24rpx;
    color: #A0522D;
  }
}

.time-tabs {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-bottom: 30rpx;

  .tab-btn {
    padding: 16rpx 32rpx;
    background: rgba(244, 162, 97, 0.1);
    border: 2rpx solid rgba(244, 162, 97, 0.3);
    border-radius: 25rpx;
    font-size: 24rpx;
    color: #A0522D;

    &.active {
      background: linear-gradient(135deg, #F4A261 0%, #E9C46A 100%);
      color: white;
      border-color: #F4A261;
    }
  }
}

.data-count-tests {
  margin-bottom: 30rpx;

  .section-title {
    display: block;
    font-size: 26rpx;
    font-weight: bold;
    color: #8B4513;
    margin-bottom: 16rpx;
  }

  .count-buttons {
    display: flex;
    gap: 16rpx;
    flex-wrap: wrap;

    .count-btn {
      padding: 12rpx 24rpx;
      background: rgba(233, 196, 106, 0.2);
      border: 1rpx solid rgba(233, 196, 106, 0.5);
      border-radius: 20rpx;
      font-size: 22rpx;
      color: #8B4513;
    }
  }
}

.debug-info {
  background: rgba(244, 162, 97, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid rgba(244, 162, 97, 0.2);

  .debug-title {
    font-size: 26rpx;
    font-weight: bold;
    color: #8B4513;
    margin-bottom: 16rpx;
  }

  .debug-item {
    display: flex;
    margin-bottom: 12rpx;

    .debug-label {
      font-size: 22rpx;
      color: #A0522D;
      font-weight: 500;
      min-width: 120rpx;
    }

    .debug-value {
      font-size: 22rpx;
      color: #8B4513;
      flex: 1;

      &.small {
        font-size: 18rpx;
        word-break: break-all;
      }
    }
  }
}

.chart-section {
  margin-bottom: 30rpx;
}

.instructions {
  background: rgba(233, 196, 106, 0.1);
  border-radius: 12rpx;
  padding: 24rpx;
  border: 2rpx solid rgba(233, 196, 106, 0.2);

  .instruction-title {
    font-size: 26rpx;
    font-weight: bold;
    color: #8B4513;
    margin-bottom: 16rpx;
  }

  .instruction-item {
    margin-bottom: 12rpx;

    text {
      font-size: 22rpx;
      color: #A0522D;
      line-height: 1.5;
    }
  }
}
</style>
