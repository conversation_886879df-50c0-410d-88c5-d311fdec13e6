/**
 * API 数据调试工具
 * 用于诊断和解决 API 响应数据结构问题
 */

/**
 * 调试 API 响应数据结构
 */
export function debugApiResponse(response: any, apiName: string) {
  console.group(`🔍 API Response Debug: ${apiName}`)
  
  console.log('Response type:', typeof response)
  console.log('Response value:', response)
  
  if (response === null) {
    console.warn('❌ Response is null')
  } else if (response === undefined) {
    console.warn('❌ Response is undefined')
  } else if (Array.isArray(response)) {
    console.log('✅ Response is array')
    console.log('Array length:', response.length)
    if (response.length > 0) {
      console.log('First item:', response[0])
      console.log('First item type:', typeof response[0])
    }
  } else if (typeof response === 'object') {
    console.log('📦 Response is object')
    console.log('Object keys:', Object.keys(response))
    
    // 检查常见的数据字段
    const commonDataFields = ['data', 'result', 'list', 'items', 'records']
    commonDataFields.forEach(field => {
      if (response[field] !== undefined) {
        console.log(`Found field "${field}":`, typeof response[field], Array.isArray(response[field]) ? `(array, length: ${response[field].length})` : '')
      }
    })
  } else {
    console.warn('⚠️ Response is primitive type:', typeof response)
  }
  
  console.groupEnd()
}

/**
 * 提取数组数据从 API 响应
 */
export function extractArrayFromResponse(response: any): any[] {
  if (!response) {
    return []
  }
  
  if (Array.isArray(response)) {
    return response
  }
  
  if (typeof response === 'object') {
    // 尝试常见的数据字段
    const possibleFields = ['data', 'result', 'list', 'items', 'records', 'content']
    
    for (const field of possibleFields) {
      if (Array.isArray(response[field])) {
        console.log(`✅ Found array data in field: ${field}`)
        return response[field]
      }
    }
    
    // 如果没有找到数组字段，检查是否有嵌套对象
    const values = Object.values(response)
    for (const value of values) {
      if (Array.isArray(value)) {
        console.log('✅ Found array in object values')
        return value
      }
    }
  }
  
  console.warn('❌ No array found in response')
  return []
}

/**
 * 验证数据项结构
 */
export function validateDataItem(item: any, index: number, expectedFields: string[]) {
  if (!item) {
    console.warn(`❌ Item at index ${index} is null or undefined`)
    return false
  }
  
  if (typeof item !== 'object') {
    console.warn(`❌ Item at index ${index} is not an object:`, typeof item)
    return false
  }
  
  const missingFields = expectedFields.filter(field => !(field in item))
  if (missingFields.length > 0) {
    console.warn(`⚠️ Item at index ${index} missing fields:`, missingFields)
  }
  
  return true
}

/**
 * 分析分时图数据结构
 */
export function analyzeTimeChartData(data: any[]) {
  console.group('📊 Time Chart Data Analysis')
  
  if (!Array.isArray(data) || data.length === 0) {
    console.warn('❌ No valid data to analyze')
    console.groupEnd()
    return
  }
  
  const expectedFields = ['date_time', 'new', 'open', 'high', 'low', 'buy', 'sell', 'diffper']
  const sampleItem = data[0]
  
  console.log('Sample item:', sampleItem)
  console.log('Sample item keys:', Object.keys(sampleItem))
  
  expectedFields.forEach(field => {
    const hasField = field in sampleItem
    const value = sampleItem[field]
    const type = typeof value
    
    console.log(`${hasField ? '✅' : '❌'} ${field}: ${hasField ? `${type} (${value})` : 'missing'}`)
  })
  
  // 检查数据类型
  const numericFields = ['new', 'open', 'high', 'low', 'buy', 'sell', 'diffper']
  numericFields.forEach(field => {
    if (field in sampleItem) {
      const value = sampleItem[field]
      const isNumeric = typeof value === 'number' || !isNaN(Number(value))
      console.log(`${isNumeric ? '✅' : '⚠️'} ${field} is ${isNumeric ? 'numeric' : 'non-numeric'}: ${value}`)
    }
  })
  
  console.groupEnd()
}

/**
 * 分析K线图数据结构
 */
export function analyzeKLineData(data: any[]) {
  console.group('📈 K-Line Data Analysis')
  
  if (!Array.isArray(data) || data.length === 0) {
    console.warn('❌ No valid data to analyze')
    console.groupEnd()
    return
  }
  
  const expectedFields = ['date_time', 'open', 'high', 'low', 'close', 'volume']
  const sampleItem = data[0]
  
  console.log('Sample item:', sampleItem)
  console.log('Sample item keys:', Object.keys(sampleItem))
  
  expectedFields.forEach(field => {
    const hasField = field in sampleItem
    const value = sampleItem[field]
    const type = typeof value
    
    console.log(`${hasField ? '✅' : '❌'} ${field}: ${hasField ? `${type} (${value})` : 'missing'}`)
  })
  
  console.groupEnd()
}

/**
 * 创建安全的数据处理函数
 */
export function createSafeDataProcessor(processorName: string) {
  return function(data: any, ...args: any[]) {
    try {
      console.group(`🔧 Safe Data Processor: ${processorName}`)
      
      if (!data) {
        console.warn('❌ Input data is null or undefined')
        return []
      }
      
      if (!Array.isArray(data)) {
        console.warn('❌ Input data is not an array, attempting to extract...')
        data = extractArrayFromResponse(data)
      }
      
      if (data.length === 0) {
        console.warn('⚠️ Input data array is empty')
        return []
      }
      
      console.log(`✅ Processing ${data.length} items`)
      console.groupEnd()
      
      return data
    } catch (error) {
      console.error(`❌ Error in ${processorName}:`, error)
      console.groupEnd()
      return []
    }
  }
}

/**
 * 监控 forEach 调用
 */
export function safeForEach<T>(array: any, callback: (item: T, index: number, array: T[]) => void, context?: string) {
  if (!array) {
    console.error(`❌ safeForEach: array is null or undefined in context: ${context || 'unknown'}`)
    return
  }
  
  if (!Array.isArray(array)) {
    console.error(`❌ safeForEach: not an array in context: ${context || 'unknown'}`, typeof array, array)
    return
  }
  
  if (typeof callback !== 'function') {
    console.error(`❌ safeForEach: callback is not a function in context: ${context || 'unknown'}`)
    return
  }
  
  try {
    array.forEach(callback)
  } catch (error) {
    console.error(`❌ safeForEach: error during iteration in context: ${context || 'unknown'}`, error)
  }
}
