/**
 * 手机端图表配置优化
 * 针对移动设备的触摸交互和显示效果进行优化
 */

// 检测是否为移动设备
export function isMobileDevice(): boolean {
  // #ifdef H5
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  // #endif
  
  // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
  return true // 小程序环境默认为移动设备
  // #endif
  
  // #ifdef APP-PLUS
  return true // App环境默认为移动设备
  // #endif
  
  return false
}

// 获取屏幕宽度
export function getScreenWidth(): number {
  // #ifdef H5
  return window.innerWidth
  // #endif
  
  // #ifndef H5
  const systemInfo = uni.getSystemInfoSync()
  return systemInfo.screenWidth
  // #endif
}

// 根据屏幕尺寸调整图表配置
export function getMobileChartConfig(timeFrame: string) {
  const screenWidth = getScreenWidth()
  const isMobile = isMobileDevice()
  const isSmallScreen = screenWidth < 375 // iPhone SE 及以下尺寸
  
  // 基础配置
  const baseConfig = {
    padding: isMobile ? [12, 12, 8, 12] : [20, 20, 10, 20],
    enableScroll: true,
    enableMarkLine: false,
    dataLabel: false,
    dataPointShape: false,
    background: 'transparent',
    pixelRatio: isMobile ? 2 : 1,
    animation: !isSmallScreen, // 小屏幕设备关闭动画以提升性能
    touchMoveLimit: isMobile ? 60 : 30,
    enableScrollX: true,
    enableScrollY: false,
  }
  
  // X轴配置
  const xAxisConfig = {
    disableGrid: false,
    type: 'category',
    gridType: 'dash',
    dashLength: 3,
    gridColor: 'rgba(212, 175, 55, 0.15)',
    fontColor: '#8B4513',
    fontSize: isSmallScreen ? 8 : (isMobile ? 9 : 10),
    rotateLabel: false,
    itemCount: getItemCount(timeFrame, screenWidth),
    scrollShow: true,
    scrollAlign: 'right',
    boundaryGap: true,
    labelCount: getLabelCount(screenWidth),
    scrollBackgroundColor: 'rgba(244, 162, 97, 0.1)',
    scrollColor: '#D4AF37',
  }
  
  // Y轴配置
  const yAxisConfig = {
    gridType: 'dash',
    dashLength: 3,
    gridColor: 'rgba(212, 175, 55, 0.15)',
    fontColor: '#8B4513',
    fontSize: isSmallScreen ? 8 : (isMobile ? 9 : 10),
    format: (val: number) => val.toFixed(2),
    splitNumber: isMobile ? 4 : 6,
  }
  
  // K线配置
  const candleConfig = {
    color: {
      upLine: '#DC143C',
      upFill: '#DC143C',
      downLine: '#228B22',
      downFill: '#228B22',
    },
    border: 1,
    width: isSmallScreen ? 0.6 : (isMobile ? 0.7 : 0.8),
    average: {
      show: true,
      name: ['MA5', 'MA10', 'MA20'],
      day: [5, 10, 20],
      color: ['#D4AF37', '#B8860B', '#CD853F'],
      lineWidth: isSmallScreen ? 1 : (isMobile ? 1.5 : 2),
    },
  }
  
  // 线图配置
  const lineConfig = {
    type: 'curve',
    width: isSmallScreen ? 2 : (isMobile ? 2.5 : 3),
    activeType: 'hollow',
    color: '#D4AF37',
    activeColor: '#B8860B',
  }
  
  // 提示框配置
  const tooltipConfig = {
    showCategory: true,
    bgColor: 'rgba(255, 255, 255, 0.95)',
    bgOpacity: 0.95,
    borderColor: '#D4AF37',
    borderWidth: 1,
    borderRadius: 8,
    fontColor: '#8B4513',
    fontSize: isSmallScreen ? 10 : (isMobile ? 11 : 12),
    offsetX: isMobile ? 10 : 0,
    offsetY: isMobile ? 10 : 0,
  }
  
  return {
    ...baseConfig,
    xAxis: xAxisConfig,
    yAxis: yAxisConfig,
    legend: { show: false },
    extra: {
      candle: candleConfig,
      line: lineConfig,
      tooltip: tooltipConfig,
      touchEventOn: isMobile ? 'mix' : 'none',
    },
  }
}

// 根据时间框架和屏幕宽度确定显示的数据点数量
function getItemCount(timeFrame: string, screenWidth: number): number {
  const isTimeChart = timeFrame === 'time'
  
  if (screenWidth < 375) {
    // 小屏幕
    return isTimeChart ? 6 : 15
  } else if (screenWidth < 414) {
    // 中等屏幕
    return isTimeChart ? 8 : 20
  } else {
    // 大屏幕
    return isTimeChart ? 10 : 25
  }
}

// 根据屏幕宽度确定标签数量
function getLabelCount(screenWidth: number): number {
  if (screenWidth < 375) {
    return 3
  } else if (screenWidth < 414) {
    return 4
  } else {
    return 5
  }
}

// 获取手机端优化的容器样式
export function getMobileContainerStyle() {
  const isMobile = isMobileDevice()
  const screenWidth = getScreenWidth()
  
  return {
    height: isMobile ? (screenWidth < 375 ? '650rpx' : '700rpx') : '800rpx',
    touchAction: 'pan-x',
    WebkitTouchCallout: 'none',
    WebkitUserSelect: 'none',
    userSelect: 'none',
    WebkitTapHighlightColor: 'transparent',
  }
}

// 获取手机端优化的图表包装器样式
export function getMobileChartWrapperStyle() {
  const isMobile = isMobileDevice()
  const screenWidth = getScreenWidth()
  
  return {
    height: isMobile ? (screenWidth < 375 ? '320rpx' : '350rpx') : '400rpx',
    touchAction: 'pan-x',
    WebkitTouchCallout: 'none',
    WebkitUserSelect: 'none',
    userSelect: 'none',
    WebkitTapHighlightColor: 'transparent',
  }
}
